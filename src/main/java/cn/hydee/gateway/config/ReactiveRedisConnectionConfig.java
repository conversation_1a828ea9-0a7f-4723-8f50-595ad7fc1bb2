package cn.hydee.gateway.config;

import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;

import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 响应式Redis连接工厂配置
 * 为ReactiveRedisTemplate提供连接工厂支持
 */
@Configuration
public class ReactiveRedisConnectionConfig {

    /**
     * 创建响应式Redis连接工厂
     * 使用Lettuce客户端支持响应式操作
     */
    @Bean
    public ReactiveRedisConnectionFactory reactiveRedisConnectionFactory(RedisProperties redisProperties) {
        // 配置Redis集群
        RedisClusterConfiguration clusterConfiguration = new RedisClusterConfiguration();

        // 从配置文件中读取集群节点
        if (redisProperties.getCluster() != null && redisProperties.getCluster().getNodes() != null) {
            List<RedisNode> nodes = redisProperties.getCluster().getNodes().stream()
                    .map(nodeStr -> {
                        String[] parts = nodeStr.split(":");
                        return new RedisNode(parts[0], Integer.parseInt(parts[1]));
                    })
                    .collect(Collectors.toList());
            clusterConfiguration.setClusterNodes(nodes);
            clusterConfiguration.setMaxRedirects(redisProperties.getCluster().getMaxRedirects());
        }

        // 设置密码
        if (redisProperties.getPassword() != null) {
            clusterConfiguration.setPassword(redisProperties.getPassword());
        }

        // 配置连接池
        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        if (redisProperties.getJedis() != null && redisProperties.getJedis().getPool() != null) {
            RedisProperties.Pool pool = redisProperties.getJedis().getPool();
            poolConfig.setMaxTotal(pool.getMaxActive());
            poolConfig.setMaxIdle(pool.getMaxIdle());
            poolConfig.setMinIdle(pool.getMinIdle());
            poolConfig.setMaxWaitMillis(pool.getMaxWait().toMillis());
        }

        // 配置集群拓扑刷新
        ClusterTopologyRefreshOptions topologyRefreshOptions = ClusterTopologyRefreshOptions.builder()
                .enablePeriodicRefresh(Duration.ofSeconds(30))
                .enableAllAdaptiveRefreshTriggers()
                .build();

        ClusterClientOptions clusterClientOptions = ClusterClientOptions.builder()
                .topologyRefreshOptions(topologyRefreshOptions)
                .build();

        // 构建Lettuce客户端配置
        LettucePoolingClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
                .poolConfig(poolConfig)
                .clientOptions(clusterClientOptions)
                .commandTimeout(Duration.ofMillis(redisProperties.getTimeout().toMillis()))
                .build();

        // 创建连接工厂
        LettuceConnectionFactory factory = new LettuceConnectionFactory(clusterConfiguration, clientConfig);
        factory.setShareNativeConnection(false); // 响应式操作不共享连接
        factory.setValidateConnection(true);

        return factory;
    }
}
