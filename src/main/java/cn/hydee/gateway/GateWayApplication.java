package cn.hydee.gateway;

import cn.hydee.starter.grey.springboot.lib.smooth.SmoothService;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import reactor.blockhound.BlockHound;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@EnableFeignClients
@EnableApolloConfig
public class GateWayApplication {

    static {
        SmoothService.enhanceShutdownApi();
    }

    public static void main(String[] args) {
        blockHound();

        System.setProperty("csp.sentinel.app.type", "1");
        SpringApplication.run(GateWayApplication.class, args);
    }

    private static void blockHound() {
        // 配置BlockHound，允许Sentinel日志相关的阻塞调用
        BlockHound.install(builder -> {
            // 允许Sentinel日志写入文件的阻塞调用
            builder.allowBlockingCallsInside("java.io.FileOutputStream", "writeBytes");
            builder.allowBlockingCallsInside("java.io.FileOutputStream", "write");
            builder.allowBlockingCallsInside("java.io.BufferedOutputStream", "flushBuffer");
            builder.allowBlockingCallsInside("java.io.BufferedOutputStream", "flush");
            builder.allowBlockingCallsInside("java.util.logging.FileHandler$MeteredStream",
                "flush");
            builder.allowBlockingCallsInside("java.util.logging.StreamHandler", "flush");
            builder.allowBlockingCallsInside("java.util.logging.FileHandler", "publish");
            builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.log.jul.DateFileLogHandler",
                "publish");

            // 允许JUL日志系统的阻塞调用
            builder.allowBlockingCallsInside("java.util.logging.Logger", "log");
            builder.allowBlockingCallsInside("java.util.logging.Logger", "doLog");
            builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.log.jul.BaseJulLogger",
                "log");
            builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.log.jul.JavaLoggingAdapter",
                "info");
            builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.log.RecordLog", "info");

            // 允许Sentinel上下文初始化相关的阻塞调用
            builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.node.DefaultNode",
                "addChild");
            builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.context.ContextUtil",
                "initDefaultContext");
        });
    }
}
